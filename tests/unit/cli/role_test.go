package cli

import (
	"context"
	"io"
	"os"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/tests/testutil"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/urfave/cli/v2"
)

var (
	sharedDB *testutil.EmbeddedDB
	roleApp  *cli.App
)

// TestMain sets up the shared database for all role tests
func TestMain(m *testing.M) {
	// Setup: Create shared database
	sharedDB = testutil.NewEmbeddedDB(&testing.T{})

	// Create the CLI app once
	roleApp = &cli.App{
		Commands: []*cli.Command{
			{
				Name: "role",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunRoleCreate,
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunRoleList,
					},
					{
						Name: "get",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunRoleGet,
					},
					{
						Name: "update",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "new-name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunRoleUpdate,
					},
					{
						Name: "delete",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunRoleDelete,
					},
				},
			},
		},
	}

	// Run tests
	code := m.Run()

	// Teardown: Close shared database
	if sharedDB != nil {
		sharedDB.Close()
	}

	os.Exit(code)
}

// Helper to clean tables between tests
func cleanupTables(t *testing.T) {
	t.Helper()
	sharedDB.CleanupTables(t)
}

func TestRunRoleCreate(t *testing.T) {
	app, embeddedDB := createTestRoleApp(t)

	// Capture stdout
	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "role", "create", "--name", "test-role", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout // Restore stdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Role created successfully")
	assert.Contains(t, string(out), "Name: test-role")

	// Verify the role was actually created in the database
	role, err := embeddedDB.Queries().GetRoleByName(context.Background(), "test-role")
	require.NoError(t, err)
	assert.Equal(t, "test-role", role.Name)
}

func TestRunRoleList(t *testing.T) {
	app, embeddedDB := createTestRoleApp(t)

	// Create test roles
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestRole(t, "admin")
	testData.CreateTestRole(t, "user")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "role", "list", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Roles:")
	assert.Contains(t, string(out), "admin")
	assert.Contains(t, string(out), "user")
}

func TestRunRoleGet(t *testing.T) {
	app, embeddedDB := createTestRoleApp(t)

	// Create test role
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestRole(t, "admin")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "role", "get", "--name", "admin", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Role Details:")
	assert.Contains(t, string(out), "Name: admin")
}

func TestRunRoleUpdate(t *testing.T) {
	app, embeddedDB := createTestRoleApp(t)

	// Create test role
	testData := testutil.NewTestData(embeddedDB.Queries())
	role := testData.CreateTestRole(t, "old-role")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "role", "update", "--name", "old-role", "--new-name", "new-role", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Role updated successfully")
	assert.Contains(t, string(out), "Name=new-role")

	// Verify the role was actually updated in the database
	updatedRole, err := embeddedDB.Queries().GetRoleByName(context.Background(), "new-role")
	require.NoError(t, err)
	assert.Equal(t, role.ID, updatedRole.ID)
	assert.Equal(t, "new-role", updatedRole.Name)
}

func TestRunRoleDelete(t *testing.T) {
	app, embeddedDB := createTestRoleApp(t)

	// Create test role
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestRole(t, "role-to-delete")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "role", "delete", "--name", "role-to-delete", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Role role-to-delete deleted successfully.")

	// Verify the role was actually deleted from the database
	_, err = embeddedDB.Queries().GetRoleByName(context.Background(), "role-to-delete")
	assert.Error(t, err) // Should not exist anymore
}
